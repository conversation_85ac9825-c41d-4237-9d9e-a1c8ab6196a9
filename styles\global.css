/* Import base styles and variables */
@import 'base.css';

/* Import component styles */
@import 'components/card.css';
@import 'components/about.css';
@import 'components/services.css';

/* Import effect styles */
@import 'effects/holographic.css';
@import 'effects/shine.css';
@import 'effects/animations.css';

/* Import responsive styles (consolidated media queries) */
@import 'responsive.css';

/* Disable main page scrollbar */
html, body {
    margin: 0;
    padding: 0;
    overflow: hidden;
    height: 100%;
    width: 100%;
}

.card-container {
    height: 100vh;
    width: 100vw;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

/* Service Area Page Styles */
.service-area-page {
    display: none;
    padding: 1rem;
    position: absolute;
    inset: 6px;
    background-color: rgb(230, 245, 245);
    z-index: 30;
    border-radius: 8px;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.05);
    overflow-y: auto;
}

.service-area-header {
    text-align: center;
    margin-bottom: 1rem;
}

.service-area-title {
    font-size: 24px;
    margin: 0;
    color: var(--text-color);
}

.map-container {
    width: 100%;
    height: 300px;
    margin: 1rem 0;
    border-radius: 8px;
    overflow: hidden;
}

#service-area-map {
    width: 100%;
    height: 100%;
}

.service-area-info {
    margin: 1rem 0;
    text-align: center;
}

.service-area-description {
    font-size: 0.9rem;
    line-height: 1.4;
    color: #666;
}

.service-area-footer {
    margin-top: 20px;
}

.service-area-buttons-container {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
}

.service-area-back-button {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s;
}

.service-area-back-button {
    background-color: #f0f0f0;
    color: #333;
}

.service-area-back-button:hover {
    background-color: #e0e0e0;
}



/* Ensure service area main title is visible and prominent */
.service-area-main-title {
  display: block;
  font-size: 2rem;
  font-weight: bold;
  text-align: center;
  margin-bottom: 0.5rem;
  color: #222;
}

/* Utility class for toggling page visibility */
.active-page {
  display: block !important;
}

/* Ensure all text and buttons in .service-area-page are visible */
.service-area-page * {
  color: inherit;
  z-index: 1;
}
