/**
 * Main Application Entry Point
 *
 * This file initializes all modules and sets up the application.
 * It handles:
 * - Module initialization
 * - Event listener setup
 * - Cleanup on page unload
 * - Error handling for critical operations
 */

// Import modules
import { initCardEffects } from './modules/card-effects.js';
import { initPageTransitions } from './modules/page-transitions.js';
import { initServices } from './modules/services.js';
import { initDeviceDetection } from './modules/device-detection.js';
import { initServiceArea } from './modules/service-area.js';

// Module instances for cleanup
let cardEffects;
let pageTransitions;
let services;
let deviceDetection;
let serviceArea;

/**
 * Initializes all application modules
 */
function initializeApp() {
    try {
        // Initialize device detection first as other modules may depend on it
        deviceDetection = initDeviceDetection();

        // Get the main card element
        const card = document.getElementById('card');
        if (!card) {
            throw new Error('Card element not found in the DOM');
        }

        // Initialize modules in sequence with error handling
        try {
            cardEffects = initCardEffects(card);
        } catch (error) {
            console.error('Failed to initialize card effects:', error);
        }

        try {
            pageTransitions = initPageTransitions(card);
        } catch (error) {
            console.error('Failed to initialize page transitions:', error);
        }

        try {
            services = initServices();
        } catch (error) {
            console.error('Failed to initialize services:', error);
        }

        try {
            serviceArea = initServiceArea();
        } catch (error) {
            console.error('Failed to initialize service area:', error);
        }
    } catch (error) {
        console.error('Critical error during application initialization:', error);
        // Display user-friendly error message if possible
        try {
            const errorMessage = document.createElement('div');
            errorMessage.style.position = 'fixed';
            errorMessage.style.top = '50%';
            errorMessage.style.left = '50%';
            errorMessage.style.transform = 'translate(-50%, -50%)';
            errorMessage.style.padding = '20px';
            errorMessage.style.backgroundColor = 'rgba(255, 0, 0, 0.8)';
            errorMessage.style.color = 'white';
            errorMessage.style.borderRadius = '5px';
            errorMessage.style.textAlign = 'center';
            errorMessage.style.zIndex = '10000';
            errorMessage.innerHTML = '<strong>Something went wrong</strong><br>Please try refreshing the page.';
            document.body.appendChild(errorMessage);
        } catch (e) {
            // Last resort - can't even show error message
        }
    }
}

// Initialize the application when the DOM is ready
document.addEventListener('DOMContentLoaded', initializeApp);

/**
 * Cleans up all module event listeners and resources
 */
function cleanupApp() {
    // Clean up all event listeners
    try {
        if (cardEffects && cardEffects.cleanup) {
            cardEffects.cleanup();
        }
    } catch (error) {
        console.error('Error during card effects cleanup:', error);
    }

    try {
        if (pageTransitions && pageTransitions.cleanup) {
            pageTransitions.cleanup();
        }
    } catch (error) {
        console.error('Error during page transitions cleanup:', error);
    }

    try {
        if (services && services.cleanup) {
            services.cleanup();
        }
    } catch (error) {
        console.error('Error during services cleanup:', error);
    }

    try {
        if (deviceDetection && deviceDetection.cleanup) {
            deviceDetection.cleanup();
        }
    } catch (error) {
        console.error('Error during device detection cleanup:', error);
    }

    try {
        if (serviceArea && serviceArea.cleanup) {
            serviceArea.cleanup();
        }
    } catch (error) {
        console.error('Error during service area cleanup:', error);
    }
}

// Clean up event listeners before page unload
window.addEventListener('beforeunload', cleanupApp);

// Update navigation logic for card pages
function showPage(pageClass) {
  const pages = document.querySelectorAll('.about-me-page, .services-page, .service-area-page, .card-header');
  pages.forEach(page => page.classList.remove('active-page'));
  document.querySelector(pageClass).classList.add('active-page');
}

document.addEventListener('DOMContentLoaded', () => {
  const servicesAreaButton = document.querySelector('.services-area-button');
  const serviceAreaBackButton = document.querySelector('.service-area-back-button');

  if (servicesAreaButton && serviceAreaBackButton) {
    servicesAreaButton.addEventListener('click', () => {
      showPage('.service-area-page');
      if (typeof initMap === 'function') initMap();
    });
    serviceAreaBackButton.addEventListener('click', () => {
      showPage('.services-page');
    });
  }
});

